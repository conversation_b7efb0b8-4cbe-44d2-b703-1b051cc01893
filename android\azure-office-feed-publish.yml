variables:
- name: tags
  value: production,externalfacing
resources:
  repositories:
  - repository: OfficePipelineTemplates
    type: git
    name: 1ESPipelineTemplates/OfficePipelineTemplates
    ref: refs/tags/release
extends:
  template: v1/Office.Unofficial.PipelineTemplate.yml@OfficePipelineTemplates
  parameters:
    pool:
      name: Azure-Pipelines-1ESPT-ExDShared
      image: windows-2022
      os: windows
    customBuildTags:
    - ES365AIMigrationTooling
    stages:
    - stage: __default
      jobs:
      - job: Compliance
        displayName: Compliance checks
        steps:
        - task: 6d15af64-176c-496d-b583-fd2ae21d4df4@1
          inputs:
            repository: self
            clean: true
            fetchDepth: 10
      - job: BuildAndPublish
        displayName: 'Build and Publish to Office Feed'
        dependsOn:
        - Compliance
        variables:
        - name: BUILDSECMON_OPT_IN
          value: true
        - name: jdkVersion
          value: '1.11'
        steps:
        - task: Gradle@3
          displayName: 'Build Release AAR'
          inputs:
            gradleWrapperFile: 'android/gradlew'
            workingDirectory: 'android'
            tasks: 'assembleRelease makeSystemIconsUniversalPkg'
            publishJUnitResults: true
            testResultsFiles: '**/TEST-*.xml'
            javaHomeOption: 'JDKVersion'
            jdkVersionOption: '$(jdkVersion)'
            sonarQubeRunAnalysis: false
            spotBugsAnalysis: false
            options: '-PversionName=$(VERSION_NAME)'
        - task: CopyFiles@2
          displayName: 'Copy Files to: $(build.artifactstagingdirectory)'
          inputs:
            SourceFolder: '$(system.defaultworkingdirectory)/android/universal'
            Contents: '**'
            TargetFolder: '$(build.artifactstagingdirectory)/outputs'
            CleanTargetFolder: true
            OverWrite: true
            preserveTimestamp: true
        - task: UniversalPackages@0
          displayName: 'Publish to Office Feed'
          inputs:
            command: 'publish'
            publishDirectory: '$(Build.ArtifactStagingDirectory)/outputs/'
            feedsToUsePublish: 'internal'
            vstsFeedPublish: 'Office'
            vstsFeedPackagePublish: 'fluentuisystemicons'
            versionOption: 'custom'
            versionPublish: '$(VERSION_NAME)'
            packagePublishDescription: 'Fluent System Icons Universal Package'
            publishedPackageVar: 'fluentsystemiconspackage'
