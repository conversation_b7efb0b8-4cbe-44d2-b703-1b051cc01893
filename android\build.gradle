// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply plugin: 'io.github.gradle-nexus.publish-plugin'

buildscript {
    ext.kotlin_version = '1.3.71'
    ext.appCenterSdkVersion = '2.5.1'
    ext.nexus_staging_plugin_version = '1.1.0'
    repositories {
        maven { url "https://plugins.gradle.org/m2/" }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "io.github.gradle-nexus:publish-plugin:$nexus_staging_plugin_version"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

project.ext {
    universalPkgDir = "universal"
}

task makeSystemIconsUniversalPkg(type: Copy) {
    description = "Prepares the fluentsystemicons AAR from the library module for universal packaging."
    duplicatesStrategy = DuplicatesStrategy.WARN
    from("library/build/outputs/aar/") {
        include "fluentsystemicons-release.aar"
    }
    into("$rootDir/${project.ext.universalPkgDir}/libs")

    includeEmptyDirs = false
    rename '(.+)-release', '$1'
}

apply from: "${rootDir}/scripts/publish-root.gradle"
